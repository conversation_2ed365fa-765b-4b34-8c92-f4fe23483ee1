Building project (fat jar)...
> Task :compileJava UP-TO-DATE
> Task :processResources UP-TO-DATE
> Task :classes UP-TO-DATE
> Task :shadowJar

[Incubating] Problems report is available at: file:///Users/<USER>/Code/TFC/pdf-engine/augment/build/reports/problems/problems-report.html

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 996ms
3 actionable tasks: 1 executed, 2 up-to-date
Running java -jar "build/libs/pdf-engine-1.0-SNAPSHOT-all.jar" examples/cv-ml.pdf -d
2025-06-05 22:04:14 [main] INFO  com.tfc.Main - Debug mode enabled with verbose logging
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'q' - Save graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: cm with operands: [COSFloat{.23999999}, COSInt{0}, COSInt{0}, COSFloat{-.23999999}, COSInt{0}, COSFloat{850.07996}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'cm' - Modify current transformation matrix: [COSFloat{.23999999}, COSInt{0}, COSInt{0}, COSFloat{-.23999999}, COSInt{0}, COSFloat{850.07996}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'q' - Save graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: re with operands: [COSInt{0}, COSInt{0}, COSFloat{2481.25}, COSFloat{3509.375}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 're' - Rectangle: java.awt.geom.Rectangle2D$Float[x=0.0,y=0.0,w=2481.25,h=3509.375]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Rectangle defined: [x=0.0, y=0.0, w=2481.25, h=3509.375]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: W* with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'W*' - Set clipping path using even-odd rule
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: n with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'n' - End path without fill or stroke
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'q' - Save graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: cm with operands: [COSFloat{3.125}, COSInt{0}, COSInt{0}, COSFloat{3.125}, COSInt{0}, COSInt{0}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'cm' - Modify current transformation matrix: [COSFloat{3.125}, COSInt{0}, COSInt{0}, COSFloat{3.125}, COSInt{0}, COSInt{0}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: RG with operands: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'RG' - Set stroking color: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: rg with operands: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'rg' - Set non-stroking color: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: gs with operands: [COSName{G3}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'gs' - Set graphics state parameters: stroking alpha=1.0, non-stroking alpha=1.0
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: re with operands: [COSInt{0}, COSInt{0}, COSInt{794}, COSInt{1123}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 're' - Rectangle: java.awt.geom.Rectangle2D$Float[x=0.0,y=0.0,w=794.0,h=1123.0]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Rectangle defined: [x=0.0, y=0.0, w=794.0, h=1123.0]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: f with operands: []
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Shape bounds: [x=0.0, y=0.0, w=794.0, h=1123.0]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Clipping bounds: [x=0.0, y=0.0, w=2481.25, h=3509.375]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Intersects clip? true
2025-06-05 22:04:14 [main] DEBUG com.tfc.state.DocumentBuilder - Adding shape element: PDFShapeElement{id='SHAPE_000001', rect=[x=0.00, y=7.83, w=595.50, h=842.25], fill=RGBA{r=255, g=255, b=255, a=255, hex=#FFFFFFFF}, stroke=null}
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'f'/'F' - Fill path
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: Q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'Q' - Restore graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: Q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'Q' - Restore graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'q' - Save graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: re with operands: [COSInt{0}, COSInt{0}, COSFloat{2481.25}, COSFloat{3509.375}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 're' - Rectangle: java.awt.geom.Rectangle2D$Float[x=0.0,y=0.0,w=2481.25,h=3509.375]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Rectangle defined: [x=0.0, y=0.0, w=2481.25, h=3509.375]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: W* with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'W*' - Set clipping path using even-odd rule
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: n with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'n' - End path without fill or stroke
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'q' - Save graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: cm with operands: [COSFloat{3.125}, COSInt{0}, COSInt{0}, COSFloat{3.125}, COSInt{0}, COSInt{0}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'cm' - Modify current transformation matrix: [COSFloat{3.125}, COSInt{0}, COSInt{0}, COSFloat{3.125}, COSInt{0}, COSInt{0}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: RG with operands: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'RG' - Set stroking color: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: rg with operands: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'rg' - Set non-stroking color: [COSInt{1}, COSInt{1}, COSInt{1}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: gs with operands: [COSName{G3}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'gs' - Set graphics state parameters: stroking alpha=1.0, non-stroking alpha=1.0
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: re with operands: [COSInt{0}, COSInt{0}, COSInt{794}, COSInt{1123}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 're' - Rectangle: java.awt.geom.Rectangle2D$Float[x=0.0,y=0.0,w=794.0,h=1123.0]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Rectangle defined: [x=0.0, y=0.0, w=794.0, h=1123.0]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: f with operands: []
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Shape bounds: [x=0.0, y=0.0, w=794.0, h=1123.0]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Clipping bounds: [x=0.0, y=0.0, w=2481.25, h=3509.375]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Intersects clip? true
2025-06-05 22:04:14 [main] DEBUG com.tfc.state.DocumentBuilder - Adding shape element: PDFShapeElement{id='SHAPE_000002', rect=[x=0.00, y=7.83, w=595.50, h=842.25], fill=RGBA{r=255, g=255, b=255, a=255, hex=#FFFFFFFF}, stroke=null}
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'f'/'F' - Fill path
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: Q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'Q' - Restore graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: Q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'Q' - Restore graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'q' - Save graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: re with operands: [COSInt{0}, COSInt{0}, COSFloat{2481.25}, COSFloat{621.12976}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 're' - Rectangle: java.awt.geom.Rectangle2D$Float[x=0.0,y=0.0,w=2481.25,h=621.12976]
2025-06-05 22:04:14 [main] INFO  com.tfc.TheFamousPDFStreamEngine - Rectangle defined: [x=0.0, y=0.0, w=2481.25, h=621.1297607421875]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: W* with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'W*' - Set clipping path using even-odd rule
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: n with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'n' - End path without fill or stroke
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: q with operands: []
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'q' - Save graphics state
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: cm with operands: [COSFloat{2.589999}, COSInt{0}, COSInt{0}, COSFloat{2.589999}, COSFloat{-41.909966}, COSFloat{-41.909966}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'cm' - Modify current transformation matrix: [COSFloat{2.589999}, COSInt{0}, COSInt{0}, COSFloat{2.589999}, COSFloat{-41.909966}, COSFloat{-41.909966}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: RG with operands: [COSFloat{.2941}, COSFloat{.2941}, COSFloat{.2941}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'RG' - Set stroking color: [COSFloat{.2941}, COSFloat{.2941}, COSFloat{.2941}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: rg with operands: [COSFloat{.2941}, COSFloat{.2941}, COSFloat{.2941}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'rg' - Set non-stroking color: [COSFloat{.2941}, COSFloat{.2941}, COSFloat{.2941}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: gs with operands: [COSName{G3}]
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Operation 'gs' - Set graphics state parameters: stroking alpha=1.0, non-stroking alpha=1.0
2025-06-05 22:04:14 [main] DEBUG com.tfc.TheFamousPDFStreamEngine - Processing PDF operation: re with operands: [COSInt{0}, COSInt{0}, COSInt{990}, COSInt{256}]
