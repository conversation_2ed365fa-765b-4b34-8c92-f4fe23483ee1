package com.tfc.state;

import java.awt.geom.Point2D;
import java.awt.geom.Rectangle2D;
import java.util.Stack;

/**
 * Manages clipping path operations in PDF processing.
 * Responsible for setting and tracking clipping paths and determining if elements intersect with the clipping path.
 */
public class ClippingPathManager {
    // Clipping path state
    private boolean hasClippingPath = false;
    private boolean isEvenOddRule = false;
    private final Stack<Boolean> clippingPathStack = new Stack<>();
    private final Stack<Boolean> clippingRuleStack = new Stack<>();
    private Rectangle2D.Float clippingPathRect = null;
    private final Stack<Rectangle2D.Float> clippingPathRectStack = new Stack<>();
    private Rectangle2D.Float currentRectangle;
    private final Stack<Rectangle2D.Float> currentRectangleStack = new Stack<>();
    private Point2D.Float currentPoint = null;
    private final Stack<Point2D.Float> currentPointStack = new Stack<>();
    private final GraphicsStateManager graphicsStateManager;

    public ClippingPathManager(GraphicsStateManager graphicsStateManager) {
        this.graphicsStateManager = graphicsStateManager;
    }

    /**
     * Sets the clipping path using the non-zero winding rule.
     */
    public void setClippingPath() {
        this.hasClippingPath = true;
        this.isEvenOddRule = false;
        if (currentRectangle != null) {
            this.clippingPathRect = (Rectangle2D.Float) currentRectangle.clone();
        }
    }

    /**
     *
     * Sets the clipping path using the even-odd rule.
     */
    public void setClippingPathEvenOdd() {
        this.hasClippingPath = true;
        this.isEvenOddRule = true;
        if (currentRectangle != null) {
            this.clippingPathRect = (Rectangle2D.Float) currentRectangle.clone();
        }
    }

    /**
     * Checks if a clipping path is active.
     * @return true if a clipping path is active, false otherwise
     */
    public boolean hasClippingPath() {
        return this.hasClippingPath;
    }

    /**
     * Checks if the even-odd rule is used for the clipping path.
     * @return true if the even-odd rule is used, false if the non-zero winding rule is used
     */
    public boolean isEvenOddRule() {
        return this.isEvenOddRule;
    }

    /**
     * Gets the current clipping path rectangle.
     * @return The current clipping path rectangle.
     */
    public Rectangle2D.Float getClippingPathRect() {
        return clippingPathRect;
    }

    /**
     * Sets the current rectangle.
     * @param currentRectangle The rectangle to set.
     */
    public void setCurrentRectangle(Rectangle2D.Float currentRectangle) {
        this.currentRectangle = currentRectangle;
    }

    /**
     * Gets the current rectangle.
     * @return The current rectangle.
     */
    public Rectangle2D.Float getCurrentRectangle() {
        return currentRectangle;
    }

    /**
     * Saves the clipping path state.
     */
    public void saveClippingPathState() {
        clippingPathStack.push(hasClippingPath);
        clippingRuleStack.push(isEvenOddRule);
        clippingPathRectStack.push(clippingPathRect != null ? 
            (Rectangle2D.Float) clippingPathRect.clone() : null);
        currentPointStack.push(currentPoint != null ? 
            (Point2D.Float) currentPoint.clone() : null);
        currentRectangleStack.push(currentRectangle != null ? 
            (Rectangle2D.Float) currentRectangle.clone() : null);
    }

    /**
     * Restores the clipping path state.
     */
    public void restoreClippingPathState() {
        if (!clippingPathStack.isEmpty()) {
            hasClippingPath = clippingPathStack.pop();
        }
        if (!clippingRuleStack.isEmpty()) {
            isEvenOddRule = clippingRuleStack.pop();
        }
        if (!clippingPathRectStack.isEmpty()) {
            clippingPathRect = clippingPathRectStack.pop();
        }
        if (!currentPointStack.isEmpty()) {
            currentPoint = currentPointStack.pop();
        }

        if (!currentRectangleStack.isEmpty()) {
            currentRectangle = currentRectangleStack.pop();
        } else {
            currentRectangle = null;
        }
    }

    /**
     * Sets the current point.
     * @param x The x-coordinate of the point.
     * @param y The y-coordinate of the point.
     */
    public void setCurrentPoint(float x, float y) {
        this.currentPoint = new Point2D.Float(x, y);
    }

    /**
     * Gets the current point.
     * @return The current point.
     */
    public Point2D.Float getCurrentPoint() {
        return currentPoint;
    }

    /**
     * Checks if a rectangle intersects with the current clipping path.
     * @param rect the rectangle to check
     * @return true if the rectangle intersects with the clipping path, false otherwise
     */
    public boolean intersectsClippingPath(Rectangle2D.Float rect) {
        // If there's no clipping path, everything is visible
        if (!hasClippingPath || clippingPathRect == null) {
            return true;
        }

        // Transform the rectangle using the current CTM
        double[] origin = graphicsStateManager.transformPoint(rect.getX(), rect.getY());
        double[] corner = graphicsStateManager.transformPoint(
            rect.getX() + rect.getWidth(), 
            rect.getY() + rect.getHeight()
        );

        // Create a transformed rectangle
        Rectangle2D.Float transformedRect = new Rectangle2D.Float(
            (float) Math.min(origin[0], corner[0]),
            (float) Math.min(origin[1], corner[1]),
            (float) Math.abs(corner[0] - origin[0]),
            (float) Math.abs(corner[1] - origin[1])
        );

        // Check if the transformed rectangle intersects with the clipping path
        return transformedRect.intersects(clippingPathRect);
    }
}
