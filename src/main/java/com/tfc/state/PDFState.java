package com.tfc.state;

import com.tfc.model.*;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.cos.COSNumber;
import org.apache.pdfbox.cos.COSString;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.font.PDFont;

import java.awt.geom.Rectangle2D;
import java.io.IOException;
import java.util.List;

/**
 * Manages the state of a PDF document during processing.
 * This class delegates to specialized manager classes for different areas of responsibility.
 */
public class PDFState {

    public final PDPage page;

    // Manager classes for different areas of responsibility
    private final GraphicsStateManager graphicsStateManager;
    private final ClippingPathManager clippingPathManager;
    private final TextStateManager textStateManager;
    private final ColorManager colorManager;
    private final ShapeManager shapeManager;
    private final ImageManager imageManager;
    private final DocumentBuilder documentBuilder;

    public PDFState(PDPage page) {
        this.page = page;

        // Initialize all manager classes
        this.documentBuilder = new DocumentBuilder();
        this.graphicsStateManager = new GraphicsStateManager();
        this.clippingPathManager = new ClippingPathManager(graphicsStateManager);
        this.colorManager = new ColorManager();
        this.textStateManager = new TextStateManager(page, graphicsStateManager, clippingPathManager, documentBuilder);
        this.shapeManager = new ShapeManager(graphicsStateManager, clippingPathManager, colorManager, documentBuilder);
        this.imageManager = new ImageManager(graphicsStateManager, clippingPathManager, documentBuilder);
    }

    // ========== GRAPHICS STATE METHODS (q/Q) ==========
    public void saveGraphicsState() {
        graphicsStateManager.saveGraphicsState();
        clippingPathManager.saveClippingPathState();
    }

    public void restoreGraphicsState() {
        graphicsStateManager.restoreGraphicsState();
        clippingPathManager.restoreClippingPathState();
    }

    public void addCM(List<COSBase> operands) {
        graphicsStateManager.addCM(operands);
    }

    // ========== TEXT STATE METHODS (BT/ET) ==========
    public void enterTextBlock() {
        textStateManager.enterTextBlock();
    }

    public void exitTextBlock() {
        textStateManager.exitTextBlock();
    }

    public void setTextMatrix(List<COSBase> operands) {
        textStateManager.setTextMatrix(operands);
    }

    public void setFontName(COSName fontName) {
        textStateManager.setFontName(fontName);
    }

    public void setFontSize(COSNumber fontSize) {
        textStateManager.setFontSize(fontSize);
    }

    public void addText(COSString cosString) {
        textStateManager.addText(cosString);
    }

    public void setCurrentFont(PDFont currentFont) {
        textStateManager.setCurrentFont(currentFont);
    }

    public void translateTextPosition(float tx, float ty) {
        textStateManager.translateTextPosition(tx, ty);
    }

    // ========== COLOR METHODS ==========
    public void setNonStrokingColor(List<COSBase> nonStrokingColor) {
        colorManager.setNonStrokingColor(nonStrokingColor);
    }

    public RGBA getNonStrokingColor() {
        return colorManager.getNonStrokingColor();
    }

    public void setStrokingColor(List<COSBase> strokingColor) {
        colorManager.setStrokingColor(strokingColor);
    }

    public RGBA getStrokingColor() {
        return colorManager.getStrokingColor();
    }

    /**
     * Sets the stroking alpha value.
     * @param strokingAlpha The stroking alpha value (0.0 to 1.0).
     */
    public void setStrokingAlpha(float strokingAlpha) {
        colorManager.setStrokingAlpha(strokingAlpha);
    }

    /**
     * Gets the stroking alpha value.
     * @return The stroking alpha value.
     */
    public float getStrokingAlpha() {
        return colorManager.getStrokingAlpha();
    }

    /**
     * Sets the non-stroking alpha value.
     * @param nonStrokingAlpha The non-stroking alpha value (0.0 to 1.0).
     */
    public void setNonStrokingAlpha(float nonStrokingAlpha) {
        colorManager.setNonStrokingAlpha(nonStrokingAlpha);
    }

    /**
     * Gets the non-stroking alpha value.
     * @return The non-stroking alpha value.
     */
    public float getNonStrokingAlpha() {
        return colorManager.getNonStrokingAlpha();
    }

    // ========== DOCUMENT STRUCTURE METHODS ==========
    public PDFPageStructure getPDFDocumentStructure() {
        return documentBuilder.getPDFPageStructure();
    }

    // ========== CLIPPING PATH METHODS ==========
    public void setClippingPath() {
        clippingPathManager.setClippingPath();
    }

    public void setClippingPathEvenOdd() {
        clippingPathManager.setClippingPathEvenOdd();
    }

    public boolean hasClippingPath() {
        return clippingPathManager.hasClippingPath();
    }

    public boolean isEvenOddRule() {
        return clippingPathManager.isEvenOddRule();
    }

    public boolean intersectsClippingPath(Rectangle2D.Float rect) {
        return clippingPathManager.intersectsClippingPath(rect);
    }

    public Rectangle2D.Float getClippingPathRect() {
        return clippingPathManager.getClippingPathRect();
    }

    // ========== SHAPE METHODS ==========
    public void setCurrentRectangle(Rectangle2D.Float currentRectangle) {
        clippingPathManager.setCurrentRectangle(currentRectangle);
    }

    public Rectangle2D.Float getCurrentRectangle() {
        return clippingPathManager.getCurrentRectangle();
    }

    public void consumeCurrentRectangleFill() {
        shapeManager.consumeCurrentRectangleFill();
    }

    public void consumeCurrentRectangleStroke() {
        shapeManager.consumeCurrentRectangleStroke();
    }

    public void consumeCurrentRectangleFillAndStroke() {
        shapeManager.consumeCurrentRectangleFillAndStroke();
    }

    public void consumeCurrentRectangleWithoutFillOrStroke() {
        shapeManager.consumeCurrentRectangleWithoutFillOrStroke();
    }

    // ========== PATH METHODS ==========
    public void setCurrentPoint(float x, float y) {
        shapeManager.setCurrentPoint(x, y);
    }

    public void createLineSegment(float x, float y) {
        shapeManager.createLineSegment(x, y);
    }

    // ========== IMAGE METHODS ==========
    public void addImage(Image imageElement) {
        imageManager.addImage(imageElement);
    }
}
