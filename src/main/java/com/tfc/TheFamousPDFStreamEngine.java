package com.tfc;

import com.tfc.model.Image;
import com.tfc.model.PDFPageStructure;
import com.tfc.model.RGBA;
import com.tfc.state.PDFState;
import org.apache.pdfbox.contentstream.PDContentStream;
import org.apache.pdfbox.contentstream.PDFStreamEngine;
import org.apache.pdfbox.contentstream.operator.Operator;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.cos.COSNumber;
import org.apache.pdfbox.cos.COSString;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDResources;
import org.apache.pdfbox.pdmodel.font.PDType3CharProc;
import org.apache.pdfbox.pdmodel.graphics.PDXObject;
import org.apache.pdfbox.pdmodel.graphics.color.PDColor;
import org.apache.pdfbox.pdmodel.graphics.form.PDTransparencyGroup;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.pdmodel.graphics.state.PDGraphicsState;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotation;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAppearanceStream;
import org.apache.pdfbox.util.Matrix;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.geom.Rectangle2D;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

class TheFamousPDFStreamEngine extends PDFStreamEngine {

    private static final Logger logger = LoggerFactory.getLogger(TheFamousPDFStreamEngine.class);

    private final List<PDFPageStructure> allPageStructures = new ArrayList<>();

    public TheFamousPDFStreamEngine() {
        super();
    }

    /**
     * Builds a PDF with all collected page structures and saves it to the specified file.
     * This method should be called after all pages have been processed.
     *
     * @param outputFile the file to save the PDF to
     * @throws IOException if an I/O error occurs
     */
    public void buildFinalPDF(File outputFile) throws IOException {
        if (!allPageStructures.isEmpty()) {
            PDFBuilder.buildMultiPagePDF(allPageStructures, outputFile);
            // Clear the list after building the PDF to avoid duplicates if this method is called again
            allPageStructures.clear();
        }
    }


    private PDFState pdfState = null;

    // Helper method to convert PDColor to RGBA
    RGBA getColorFromPDColor(PDColor pdColor, float alphaConstant) {
        if (pdColor == null) {
            // Return transparent color instead of null
            RGBA rgba = new RGBA();
            rgba.r = 0;
            rgba.g = 0;
            rgba.b = 0;
            rgba.a = 0; // Transparent
            return rgba;
        }

        RGBA rgba = new RGBA();
        try {
            // Get the color values directly from the PDColor components
            float[] components = pdColor.getComponents();
            if (components != null && components.length > 0) {
                // Handle different color spaces appropriately
                if (components.length >= 3) {
                    // RGB or similar color space
                    rgba.r = Math.min(255, Math.max(0, (int) (components[0] * 255)));
                    rgba.g = Math.min(255, Math.max(0, (int) (components[1] * 255)));
                    rgba.b = Math.min(255, Math.max(0, (int) (components[2] * 255)));
                    rgba.a = Math.round(alphaConstant * 255);
                } else if (components.length == 1) {
                    // For grayscale, use a heuristic approach to infer colors
                    // This will be set based on the shape type in the calling method
                    int gray = Math.min(255, Math.max(0, (int) (components[0] * 255)));

                    // Default to the original grayscale value
                    rgba.r = gray;
                    rgba.g = gray;
                    rgba.b = gray;
                    rgba.a = Math.round(alphaConstant * 255);

                    // The calling method will override these values based on shape type
                }
            } else {
                // Fallback to toRGB if components are not available
                int rgbInt = pdColor.toRGB();
                rgba.r = (rgbInt >> 16) & 0xFF;
                rgba.g = (rgbInt >> 8) & 0xFF;
                rgba.b = rgbInt & 0xFF;
                rgba.a = Math.round(alphaConstant * 255);
            }
        } catch (IOException e) {
            logger.error("Error converting PDColor to RGB: {}", e.getMessage());
            // Set to completely transparent
            rgba.r = 0;
            rgba.g = 0;
            rgba.b = 0;
            rgba.a = 0; // Opaque dark gray
        }
        return rgba;
    }

    @Override
    public void processPage(PDPage page) throws IOException {
        pdfState = new PDFState(page);
        super.processPage(page);
        // Add the current page structure to the list of all page structures
        allPageStructures.add(pdfState.getPDFDocumentStructure());
    }

    @Override
    protected void processOperator(Operator operator, List<COSBase> operands) throws IOException {
        String op = operator.getName();
        logger.debug("Processing PDF operation: {} with operands: {}", op, operands);
        PDGraphicsState graphicsState = getGraphicsState();
        switch (op) {
            case "re" -> handleRectangle(operands);
            case "l" -> handleLineTo(operands);
            case "m" -> handleMoveTo(operands);
            case "c" -> handleCubicBezierCurve(operands);
            case "v" -> handleCubicBezierCurveV(operands);
            case "y" -> handleCubicBezierCurveY(operands);
            case "h" -> handleCloseSubpath();
            case "s" -> handleCloseAndStroke(graphicsState);
            case "S" -> handleStrokePath();
            case "f", "F" -> handleFillPath();
            case "f*" -> handleFillPathEvenOdd();
            case "b" -> handleFillAndStrokePath();
            case "B" -> handleFillAndStrokePath();
            case "b*" -> handleFillAndStrokePath();
            case "B*" -> handleFillAndStrokePath();
            case "BDC" -> handleBeginMarkedContentSequence(operands);
            case "EMC" -> handleEndMarkedContentSequence();
            case "BT" -> handleBeginTextObject();
            case "ET" -> handleEndTextObject();
            case "cm" -> handleModifyCurrentTransformationMatrix(operands);
            case "Do" -> handleInvokeXObject(operands);
            case "gs" -> handleSetGraphicsStateParameters(operands);
            case "n" -> handleEndPathWithoutFillOrStroke();
            case "q" -> handleSaveGraphicsState();
            case "Q" -> handleRestoreGraphicsState();
            case "rg" -> handleSetNonStrokingColor(operands);
            case "RG" -> handleSetStrokingColor(operands);
            case "Td" -> handleMoveTextPosition(operands);
            case "Tf" -> handleSetTextFontAndSize(operands);
            case "Tj" -> handleShowText(operands);
            case "Tm" -> handleSetTextMatrix(operands);
            case "W" -> handleSetClippingPath();
            case "W*" -> handleSetClippingPathEvenOdd();
            default -> logger.warn("Unsupported operator: {}", op);
        }
        super.processOperator(operator, operands);
    }

    /**
     * 're' - Append Rectangle to Path
     * PDF Operator: re x y w h
     * Adds a rectangle to the current path at position (x, y) with width w and height h.
     * Operands: [x, y, w, h] (all COSNumber)
     * Information: Rectangle's lower-left corner (x, y), width, height.
     */
    private void handleRectangle(List<COSBase> operands) {
        float x = ((COSNumber) operands.get(0)).floatValue();
        float y = ((COSNumber) operands.get(1)).floatValue();
        float w = ((COSNumber) operands.get(2)).floatValue();
        float h = ((COSNumber) operands.get(3)).floatValue();
        Rectangle2D.Float rect = new Rectangle2D.Float(x, y, w, h);
        pdfState.setCurrentRectangle(rect);
        logger.debug("Rectangle: {}", rect);
    }

    /**
     * 'l' - Line To
     * PDF Operator: l x y
     * Appends a straight line from the current point to the point (x, y).
     * Operands: [x, y] (both COSNumber)
     * Information: End point of the line.
     */
    private void handleLineTo(List<COSBase> operands) {
        if (operands.size() == 2) {
            float x = ((COSNumber) operands.get(0)).floatValue();
            float y = ((COSNumber) operands.get(1)).floatValue();
            pdfState.createLineSegment(x, y);
            logger.debug("Line to: ({}, {})", x, y);
        } else {
            logger.warn("Invalid operands for Line to: {}", operands);
        }
    }

    /**
     * 'm' - Move To
     * PDF Operator: m x y
     * Sets the current point to (x, y) without drawing anything.
     * Operands: [x, y] (both COSNumber)
     * Information: New current point.
     */
    private void handleMoveTo(List<COSBase> operands) {
        if (operands.size() == 2) {
            float x = ((COSNumber) operands.get(0)).floatValue();
            float y = ((COSNumber) operands.get(1)).floatValue();
            pdfState.setCurrentPoint(x, y);
            logger.debug("Move to: ({}, {})", x, y);
        } else {
            logger.warn("Invalid operands for Move to: {}", operands);
        }
    }

    /**
     * 'c' - Bezier Curve To (Cubic)
     * PDF Operator: c x1 y1 x2 y2 x3 y3
     * Appends a cubic Bezier curve from the current point to (x3, y3) using (x1, y1) and (x2, y2) as control points.
     * Operands: [x1, y1, x2, y2, x3, y3] (all COSNumber)
     * Information: Control points and end point of the curve.
     */
    private void handleCubicBezierCurve(List<COSBase> operands) {
        if (operands.size() == 6) {
            //TODO
        }
        logger.debug("TODO: Bezier curve: {}", operands);
    }

    /**
     * 'v' - Bezier Curve To (Cubic, initial control point is current point)
     * PDF Operator: v x2 y2 x3 y3
     * Appends a cubic Bezier curve from the current point to (x3, y3) using the current point as the first control point and (x2, y2) as the second control point.
     * Operands: [x2, y2, x3, y3] (all COSNumber)
     * Information: Second control point and end point of the curve.
     */
    private void handleCubicBezierCurveV(List<COSBase> operands) {
        if (operands.size() == 4) {
            //TODO
        }
        logger.debug("TODO: Bezier curve V: {}", operands);
    }

    /**
     * 'y' - Bezier Curve To (Cubic, final control point is end point)
     * PDF Operator: y x1 y1 x3 y3
     * Appends a cubic Bezier curve from the current point to (x3, y3) using (x1, y1) as the first control point and (x3, y3) as the second control point.
     * Operands: [x1, y1, x3, y3] (all COSNumber)
     * Information: First control point and end point of the curve.
     */
    private void handleCubicBezierCurveY(List<COSBase> operands) {
        if (operands.size() == 4) {
            //TODO
        }
        logger.debug("TODO: Bezier curve Y: {}", operands);
    }

    /**
     * 'h' - Close Subpath
     * PDF Operator: h
     * Closes the current subpath by drawing a straight line from the current point to the starting point of the subpath.
     * Operands: []
     * Information: No operands; closes the path.
     */
    private void handleCloseSubpath() {
        //TODO
        logger.debug("TODO: Close subpath");
    }

    /**
     * 's' - Close and Stroke Path
     * PDF Operator: s
     * Closes the current subpath and strokes the path.
     * Operands: []
     * Information: No operands; closes and strokes the path.
     */
    private void handleCloseAndStroke(PDGraphicsState graphicsState) {
        // Use the stroking alpha value from our ColorManager
        float strokingAlpha = pdfState.getStrokingAlpha();
        RGBA backgroundColor = getColorFromPDColor(graphicsState.getStrokingColor(), strokingAlpha);
        logger.debug("Close and stroke: {}", backgroundColor);
        pdfState.consumeCurrentRectangleStroke();
    }

    /**
     * 'S' - Stroke Path
     * PDF Operator: S
     * Strokes the current path.
     * Operands: []
     * Information: No operands; strokes the path.
     */
    private void handleStrokePath() {
        pdfState.consumeCurrentRectangleStroke();
        logger.debug("Stroke path");
    }

    /**
     * 'f', 'F' - Fill Path (Nonzero Winding Number Rule)
     * PDF Operator: f or F
     * Fills the current path using the nonzero winding number rule.
     * Operands: []
     * Information: No operands; fills the path.
     */
    private void handleFillPath() {
        pdfState.consumeCurrentRectangleFill();
        logger.debug("Fill path");
    }

    /**
     * 'f*' - Fill Path (Even-Odd Rule)
     * PDF Operator: f*
     * Fills the current path using the even-odd rule.
     * Operands: []
     * Information: No operands; fills the path.
     */
    private void handleFillPathEvenOdd() {
        logger.debug("TODO: Fill path even odd");
    }

    /**
     * 'b' - Fill and Stroke Path (Nonzero Winding Number Rule)
     * PDF Operator: b
     * Fills and then strokes the path using the nonzero winding number rule.
     * Operands: []
     * Information: No operands; fills and strokes the path.
     * <p>
     * 'B' - Fill and Stroke Path (Nonzero Winding Number Rule, capital)
     * PDF Operator: B
     * Fills and then strokes the path using the nonzero winding number rule.
     * Operands: []
     * Information: No operands; fills and strokes the path.
     * <p>
     * 'b*' - Fill and Stroke Path (Even-Odd Rule)
     * PDF Operator: b*
     * Fills and then strokes the path using the even-odd rule.
     * Operands: []
     * Information: No operands; fills and strokes the path.
     * <p>
     * 'B*' - Fill and Stroke Path (Even-Odd Rule, capital)
     * PDF Operator: B*
     * Fills and then strokes the path using the even-odd rule.
     * Operands: []
     * Information: No operands; fills and strokes the path.
     */
    private void handleFillAndStrokePath() {
        pdfState.consumeCurrentRectangleFillAndStroke();
        logger.debug("Fill and stroke path");
    }

    /**
     * 'BDC' - Begin Marked Content Sequence
     * PDF Operator: BDC tag properties
     * Begins a marked-content sequence with an associated property list.
     * Operands: [tag (COSName), properties (COSBase)]
     * Information: Tag name and property dictionary.
     */
    private void handleBeginMarkedContentSequence(List<COSBase> operands) {
        // ignore
        // Begin marked content sequence (structure info) - no-op for now
    }

    /**
     * 'EMC' - End Marked Content Sequence
     * PDF Operator: EMC
     * Ends a marked-content sequence begun by BMC or BDC.
     * Operands: []
     * Information: No operands; ends the marked content sequence.
     */
    private void handleEndMarkedContentSequence() {
        // ignore
        // End marked content sequence - no-op for now
    }

    /**
     * 'BT' - Begin Text Object
     * PDF Operator: BT
     * Begins a text object, initializing the text matrix and text line matrix.
     * Operands: []
     * Information: No operands; begins text object.
     */
    private void handleBeginTextObject() {
        pdfState.enterTextBlock();
    }

    /**
     * 'ET' - End Text Object
     * PDF Operator: ET
     * Ends a text object, discarding the text matrix.
     * Operands: []
     * Information: No operands; ends text object.
     */
    private void handleEndTextObject() {
        pdfState.exitTextBlock();
    }

    /**
     * 'cm' - Modify Current Transformation Matrix
     * PDF Operator: cm a b c d e f
     * Modifies the current transformation matrix (CTM) with the specified matrix.
     * Operands: [a, b, c, d, e, f] (all COSNumber)
     * Information: Transformation matrix values.
     */
    private void handleModifyCurrentTransformationMatrix(List<COSBase> operands) {
        pdfState.addCM(operands);
        logger.debug("Modify current transformation matrix: {}", operands);
    }

    /**
     * 'Do' - Invoke Named XObject
     * PDF Operator: Do name
     * Paints the specified XObject (image or form) onto the page.
     * Operands: [name (COSName)]
     * Information: Name of the XObject to draw; can be image or form.
     */
    private void handleInvokeXObject(List<COSBase> operands) throws IOException {
        // Draw XObject (image or form)
        if (operands.size() == 1 && operands.getFirst() instanceof COSName xObjectName) {
            PDResources resources = getResources();
            if (resources != null) {
                PDXObject xObject = resources.getXObject(xObjectName);
                if (xObject instanceof PDImageXObject image) {
                    Image imageElement = new Image();
                    imageElement.bufferedImage = image.getImage();
                    pdfState.addImage(imageElement);
                    logger.debug("Invoke XObject: {}", operands);
                } else {
                    logger.warn("Unsupported XObject type: {}", xObject.getClass().getName());
                }
            }
        } else {
            logger.warn("Unsupported operands for XObject to: {}", operands);
        }
    }

    /**
     * 'gs' - Set Graphics State Parameters
     * PDF Operator: gs name
     * Sets the graphics state parameters from the ExtGState resource with the given name.
     * Operands: [name (COSName)]
     * Information: Name of the ExtGState resource.
     * {
     * /CA: 1,           // stroking alpha
     * /ca: 1,           // non-stroking alpha
     * /LC: 0,           // line cap: butt
     * /LJ: 0,           // line join: miter
     * /LW: 1,           // line width
     * /ML: 4,           // miter limit
     * /SA: true,        // stroke adjustment
     * /BM: /Normal      // blend mode
     * }
     * | Key   | Description                 - | Value & Meaning                                                  |
     * |-------|-------------------------------|------------------------------------------------------------------|
     * | `/CA` | Stroking alpha (opacity)      | `1` = fully opaque stroke                                        |
     * | `/ca` | Non-stroking alpha (opacity)  | `1` = fully opaque fill                                          |
     * | `/LC` | Line cap style                | `0` = butt (flat line ends)                                      |
     * | `/LJ` | Line join style               | `0` = miter (sharp corners)                                      |
     * | `/LW` | Line width                    | `1` unit                                                         |
     * | `/ML` | Miter limit                   | `4` (max ratio before bevel join)                                |
     * | `/SA` | Stroke adjustment             | `true` (align strokes to pixel grid if possible)                 |
     * | `/BM` | Blend mode                    | `/Normal` (default paint over)                                   |
     */
    private void handleSetGraphicsStateParameters(List<COSBase> operands) {
        PDExtendedGraphicsState extGState = pdfState.page.getResources().getExtGState((COSName) operands.getFirst());

        // Extract alpha values from the extended graphics state with null checks
        Float strokingAlphaObj = extGState.getStrokingAlphaConstant();
        Float nonStrokingAlphaObj = extGState.getNonStrokingAlphaConstant();

        // Use default value of 1.0f (fully opaque) if alpha values are null
        float strokingAlpha = (strokingAlphaObj != null) ? strokingAlphaObj : 1.0f;
        float nonStrokingAlpha = (nonStrokingAlphaObj != null) ? nonStrokingAlphaObj : 1.0f;

        // Set alpha values in the PDF state
        pdfState.setStrokingAlpha(strokingAlpha);
        pdfState.setNonStrokingAlpha(nonStrokingAlpha);

        logger.debug("Set graphics state parameters: stroking alpha={}, non-stroking alpha={}", 
                     strokingAlpha, nonStrokingAlpha);
    }

    /**
     * 'n' - End Path Without Fill or Stroke
     * PDF Operator: n
     * Ends the current path without filling or stroking it.
     * Operands: []
     * Information: No operands; ends the path.
     */
    private void handleEndPathWithoutFillOrStroke() {
        pdfState.consumeCurrentRectangleWithoutFillOrStroke();
        logger.debug("End path without fill or stroke");
    }

    /**
     * 'q' - Save Graphics State
     * PDF Operator: q
     * Saves the current graphics state on the stack.
     * Operands: []
     * Information: No operands; pushes graphics state.
     */
    private void handleSaveGraphicsState() {
        pdfState.saveGraphicsState();
        logger.debug("Save graphics state");
    }

    /**
     * 'Q' - Restore Graphics State
     * PDF Operator: Q
     * Restores the graphics state from the stack.
     * Operands: []
     * Information: No operands; pops graphics state.
     */
    private void handleRestoreGraphicsState() {
        pdfState.restoreGraphicsState();
        logger.debug("Restore graphics state");
    }

    /**
     * 'rg' - Set Non-Stroking Color (RGB)
     * PDF Operator: rg r g b
     * Sets the non-stroking color to the specified RGB values.
     * Operands: [r, g, b] (all COSNumber, 0..1)
     * Information: Red, green, blue color components for non-stroking operations.
     */
    private void handleSetNonStrokingColor(List<COSBase> operands) {
        pdfState.setNonStrokingColor(operands);
        logger.debug("Set non-stroking color: {}", operands);
    }

    /**
     * 'RG' - Set Stroking Color (RGB)
     * PDF Operator: RG r g b
     * Sets the stroking color to the specified RGB values.
     * Operands: [r, g, b] (all COSNumber, 0..1)
     * Information: Red, green, blue color components for stroking operations.
     */
    private void handleSetStrokingColor(List<COSBase> operands) {
        pdfState.setStrokingColor(operands);
        logger.debug("Set stroking color: {}", operands);
    }

    /**
     * 'Td' - Move Text Position
     * PDF Operator: Td tx ty
     * Moves the text position to a new location by (tx, ty).
     * Operands: [tx, ty] (both COSNumber)
     * Information: Translation values for text position.
     */
    private void handleMoveTextPosition(List<COSBase> operands) {
        float tx = ((COSNumber) operands.get(0)).floatValue();
        float ty = ((COSNumber) operands.get(1)).floatValue();
        pdfState.translateTextPosition(tx, ty);
    }

    /**
     * 'Tf' - Set Text Font and Size
     * PDF Operator: Tf font size
     * Sets the text font and size for subsequent text operations.
     * Operands: [font (COSName), size (COSNumber)]
     * Information: Font resource name and font size.
     */
    private void handleSetTextFontAndSize(List<COSBase> operands) {
        pdfState.setFontName((COSName) operands.getFirst());
        pdfState.setFontSize((COSNumber) operands.get(1));
    }

    /**
     * 'Tj' - Show Text
     * PDF Operator: Tj string
     * Shows a text string at the current text position.
     * Operands: [string (COSString)]
     * Information: Text string to display.
     */
    private void handleShowText(List<COSBase> operands) {
        pdfState.addText((COSString) operands.getFirst());
    }

    /**
     * 'Tm' - Set Text Matrix
     * PDF Operator: Tm a b c d e f
     * Sets the text matrix and text line matrix for text positioning.
     * Operands: [a, b, c, d, e, f] (all COSNumber)
     * Information: Text transformation matrix values.
     */
    private void handleSetTextMatrix(List<COSBase> operands) {
        pdfState.setTextMatrix(operands);
    }

    /**
     * 'W' - Set Clipping Path Using Non-Zero Winding Rule
     * PDF Operator: W
     * Modifies the current clipping path using the non-zero winding rule.
     * Operands: []
     * Information: No operands; sets clipping path.
     */
    private void handleSetClippingPath() {
        pdfState.setClippingPath();
        logger.debug("Set clipping path using non-zero winding rule");
    }

    /**
     * 'W*' - Set Clipping Path Using Even-Odd Rule
     * PDF Operator: W*
     * Modifies the current clipping path using the even-odd rule.
     * Operands: []
     * Information: No operands; sets clipping path.
     */
    private void handleSetClippingPathEvenOdd() {
        pdfState.setClippingPathEvenOdd();
        logger.debug("Set clipping path using even-odd rule");
    }

    @Override
    public void processOperator(String operation, List<COSBase> arguments) throws IOException {
        super.processOperator(operation, arguments);
    }

    @Override
    protected void processChildStream(PDContentStream contentStream, PDPage page) throws IOException {
        super.processChildStream(contentStream, page);
    }

    @Override
    protected void processSoftMask(PDTransparencyGroup group) throws IOException {
        super.processSoftMask(group);
    }

    @Override
    protected void processTransparencyGroup(PDTransparencyGroup group) throws IOException {
        super.processTransparencyGroup(group);
    }

    @Override
    protected void processType3Stream(PDType3CharProc charProc, Matrix textRenderingMatrix) throws IOException {
        super.processType3Stream(charProc, textRenderingMatrix);
    }

    @Override
    protected void processAnnotation(PDAnnotation annotation, PDAppearanceStream appearance) throws IOException {
        super.processAnnotation(annotation, appearance);
    }

}
