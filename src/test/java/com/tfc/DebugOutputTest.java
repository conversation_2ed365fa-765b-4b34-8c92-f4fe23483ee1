package com.tfc;

import org.apache.pdfbox.cos.COSFloat;
import org.apache.pdfbox.contentstream.operator.Operator;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Simple test to demonstrate the debug output functionality.
 */
public class DebugOutputTest {

    @Test
    void demonstrateDebugOutput() throws IOException {
        System.out.println("=== Starting Debug Output Demonstration ===");
        
        TheFamousPDFStreamEngine engine = new TheFamousPDFStreamEngine();
        PDDocument document = new PDDocument();
        PDPage page = new PDPage();
        document.addPage(page);

        // Process a page to initialize the PDF state
        engine.processPage(page);

        // Create operands for a rectangle: x=10, y=20, width=100, height=50
        List<COSBase> rectOperands = Arrays.asList(
            new COSFloat(10.0f),  // x
            new COSFloat(20.0f),  // y
            new COSFloat(100.0f), // width
            new COSFloat(50.0f)   // height
        );

        // Create operators
        Operator rectOperator = Operator.getOperator("re");
        Operator fillOperator = Operator.getOperator("f");

        System.out.println("\n--- Processing Rectangle Operator ---");
        engine.processOperator(rectOperator, rectOperands);

        System.out.println("\n--- Processing Fill Operator ---");
        engine.processOperator(fillOperator, Arrays.asList());

        System.out.println("\n=== Debug Output Demonstration Complete ===");

        document.close();
    }

    @Test
    void demonstrateClippingDebugOutput() throws IOException {
        System.out.println("=== Starting Clipping Debug Output Demonstration ===");

        TheFamousPDFStreamEngine engine = new TheFamousPDFStreamEngine();
        PDDocument document = new PDDocument();
        PDPage page = new PDPage();
        document.addPage(page);

        // Process a page to initialize the PDF state
        engine.processPage(page);

        // First, create a clipping path by defining a rectangle and setting it as clipping
        List<COSBase> clippingRectOperands = Arrays.asList(
            new COSFloat(0.0f),   // x
            new COSFloat(0.0f),   // y
            new COSFloat(50.0f),  // width
            new COSFloat(50.0f)   // height
        );

        Operator rectOperator = Operator.getOperator("re");
        Operator clippingOperator = Operator.getOperator("W");

        // Set up clipping path
        System.out.println("\n--- Setting up clipping path ---");
        engine.processOperator(rectOperator, clippingRectOperands);
        engine.processOperator(clippingOperator, Arrays.asList());

        // Now create a rectangle that partially intersects with the clipping path
        List<COSBase> shapeRectOperands = Arrays.asList(
            new COSFloat(25.0f),  // x - partially overlaps clipping
            new COSFloat(25.0f),  // y - partially overlaps clipping
            new COSFloat(100.0f), // width - extends beyond clipping
            new COSFloat(100.0f)  // height - extends beyond clipping
        );

        Operator fillOperator = Operator.getOperator("f");

        // Process the shape rectangle and fill - this should show clipping intersection
        System.out.println("\n--- Processing Rectangle that intersects clipping path ---");
        engine.processOperator(rectOperator, shapeRectOperands);
        engine.processOperator(fillOperator, Arrays.asList());

        // Now create a rectangle that is completely outside the clipping path
        List<COSBase> outsideRectOperands = Arrays.asList(
            new COSFloat(100.0f), // x - completely outside clipping
            new COSFloat(100.0f), // y - completely outside clipping
            new COSFloat(50.0f),  // width
            new COSFloat(50.0f)   // height
        );

        // Process the outside rectangle and fill - this should show no intersection
        System.out.println("\n--- Processing Rectangle outside clipping path ---");
        engine.processOperator(rectOperator, outsideRectOperands);
        engine.processOperator(fillOperator, Arrays.asList());

        System.out.println("\n=== Clipping Debug Output Demonstration Complete ===");

        document.close();
    }
}
