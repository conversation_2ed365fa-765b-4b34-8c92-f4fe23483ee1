package com.tfc;

import org.apache.pdfbox.cos.COSFloat;
import org.apache.pdfbox.contentstream.operator.Operator;
import org.apache.pdfbox.cos.COSBase;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.geom.Rectangle2D;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to demonstrate the clipping debug functionality.
 * This test shows how the debugging logs will appear when processing rectangles and fill operations.
 */
public class ClippingDebugTest {

    private static final Logger logger = LoggerFactory.getLogger(ClippingDebugTest.class);
    private TheFamousPDFStreamEngine engine;
    private PDDocument document;
    private PDPage page;

    @BeforeEach
    void setUp() throws IOException {
        engine = new TheFamousPDFStreamEngine();
        document = new PDDocument();
        page = new PDPage();
        document.addPage(page);
    }

    @Test
    void testRectangleAndFillDebugLogging() throws IOException {
        // Process a page to initialize the PDF state
        engine.processPage(page);

        // Create operands for a rectangle: x=10, y=20, width=100, height=50
        List<COSBase> rectOperands = Arrays.asList(
            new COSFloat(10.0f),  // x
            new COSFloat(20.0f),  // y
            new COSFloat(100.0f), // width
            new COSFloat(50.0f)   // height
        );

        // Create operators
        Operator rectOperator = Operator.getOperator("re");
        Operator fillOperator = Operator.getOperator("f");

        // Process the rectangle operator - this should log rectangle debug info
        logger.info("=== Testing Rectangle Debug Logging ===");
        engine.processOperator(rectOperator, rectOperands);

        // Process the fill operator - this should log fill debug info
        logger.info("=== Testing Fill Debug Logging ===");
        engine.processOperator(fillOperator, Arrays.asList());

        // The test passes if no exceptions are thrown
        // The actual debug output will be visible in the logs
        assertTrue(true, "Debug logging test completed successfully");
    }

    @Test
    void testRectangleAndFillWithClippingPath() throws IOException {
        // Process a page to initialize the PDF state
        engine.processPage(page);

        // First, create a clipping path by defining a rectangle and setting it as clipping
        List<COSBase> clippingRectOperands = Arrays.asList(
            new COSFloat(0.0f),   // x
            new COSFloat(0.0f),   // y
            new COSFloat(50.0f),  // width
            new COSFloat(50.0f)   // height
        );

        Operator rectOperator = Operator.getOperator("re");
        Operator clippingOperator = Operator.getOperator("W");

        // Set up clipping path
        logger.info("=== Setting up clipping path ===");
        engine.processOperator(rectOperator, clippingRectOperands);
        engine.processOperator(clippingOperator, Arrays.asList());

        // Now create a rectangle that partially intersects with the clipping path
        List<COSBase> shapeRectOperands = Arrays.asList(
            new COSFloat(25.0f),  // x - partially overlaps clipping
            new COSFloat(25.0f),  // y - partially overlaps clipping
            new COSFloat(100.0f), // width - extends beyond clipping
            new COSFloat(100.0f)  // height - extends beyond clipping
        );

        Operator fillOperator = Operator.getOperator("f");

        // Process the shape rectangle and fill - this should show clipping intersection
        logger.info("=== Testing Rectangle with Clipping Path ===");
        engine.processOperator(rectOperator, shapeRectOperands);
        engine.processOperator(fillOperator, Arrays.asList());

        assertTrue(true, "Clipping debug test completed successfully");
    }

    @Test
    void testRectangleOutsideClippingPath() throws IOException {
        // Process a page to initialize the PDF state
        engine.processPage(page);

        // First, create a small clipping path
        List<COSBase> clippingRectOperands = Arrays.asList(
            new COSFloat(0.0f),   // x
            new COSFloat(0.0f),   // y
            new COSFloat(50.0f),  // width
            new COSFloat(50.0f)   // height
        );

        Operator rectOperator = Operator.getOperator("re");
        Operator clippingOperator = Operator.getOperator("W");

        // Set up clipping path
        logger.info("=== Setting up small clipping path ===");
        engine.processOperator(rectOperator, clippingRectOperands);
        engine.processOperator(clippingOperator, Arrays.asList());

        // Now create a rectangle that is completely outside the clipping path
        List<COSBase> shapeRectOperands = Arrays.asList(
            new COSFloat(100.0f), // x - completely outside clipping
            new COSFloat(100.0f), // y - completely outside clipping
            new COSFloat(50.0f),  // width
            new COSFloat(50.0f)   // height
        );

        Operator fillOperator = Operator.getOperator("f");

        // Process the shape rectangle and fill - this should show no intersection
        logger.info("=== Testing Rectangle Outside Clipping Path ===");
        engine.processOperator(rectOperator, shapeRectOperands);
        engine.processOperator(fillOperator, Arrays.asList());

        assertTrue(true, "Outside clipping debug test completed successfully");
    }
}
